{
  "name": "<PERSON>-<PERSON><PERSON>-<PERSON>-Pod",
  "allowedCudaVersions": [
    "12.8"
  ],
  "imageName": "runpod/pytorch:2.8.0-py3.11-cuda12.8.1-cudnn-devel-ubuntu22.04",
  "gpuCount": 1,
  "gpuTypeIds": [
    "NVIDIA RTX A6000",
  ],
  "computeType": "GPU",
  "minRAMPerGPU": 16,
  "minVCPUPerGPU": 2,
  "containerDiskInGb": 30,
  "volumeInGb": 50,
  "vcpuCount": 2,
  "ports": ["22/tcp"],
  "volumeMountPath": "/workspace"
  
}