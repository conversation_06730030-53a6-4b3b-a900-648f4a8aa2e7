name: Deploy Private Repo to RunPod

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Create RunPod Pod
        id: create_pod
        run: |
          # Read the pod configuration from the file
          POD_CONFIG=$(cat runpod_configs/default.json | jq -c .)

          # Construct the full JSON payload for the RunPod API
          JSON_PAYLOAD=$(jq -n --argjson config "$POD_CONFIG" --arg repo_url "**************:${{ github.repository }}.git" \
            '{
              "name": $config.name,
              "allowedCudaVersions": $config.allowedCudaVersions,
              "cloudType": "SECURE",
              "imageName": $config.imageName,
              "gpuCount": $config.gpuCount,
              "gpuTypeIds": $config.gpuTypeIds,
              "computeType": $config.computeType,
              "minRAMPerGPU": $config.minRAMPerGPU,
              "minVCPUPerGPU": $config.minVCPUPerGPU,
              "containerDiskInGb": $config.containerDiskInGb,
              "volumeInGb": $config.volumeInGb,
              "vcpuCount": $config.vcpuCount,
              "ports": $config.ports,
              "volumeMountPath": $config.volumeMountPath,
              "env": {
                "GIT_REPO_URL": $repo_url
              }
            }')

          # Make the API call
          response=$(curl -s -X POST \
            -H "Authorization: Bearer ${{ secrets.RUNPOD_API_KEY }}" \
            -H "Content-Type: application/json" \
            -d "$JSON_PAYLOAD" \
            https://api.runpod.io/v2/pods)

          # Extract pod details for subsequent steps
          echo "POD_RESPONSE: $response"
          echo "pod_ip=$(echo $response | jq -r '.publicIp')" >> $GITHUB_OUTPUT
          echo "pod_id=$(echo $response | jq -r '.id')" >> $GITHUB_OUTPUT


      - name: Wait for SSH to be ready
        run: |
          IP=${{ steps.create_pod.outputs.pod_ip }}
          echo "Waiting for SSH on $IP..."
          for i in {1..10}; do
            if ssh -o StrictHostKeyChecking=no -o ConnectTimeout=5 -p 22 root@$IP exit; then
              echo "SSH is ready!"
              exit 0
            fi
            echo "Attempt $i failed, retrying in 10 seconds..."
            sleep 10
          done
          echo "SSH connection timed out."
          exit 1
        env:
           SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }} # Assumes you have a key for the action to connect to the pod


      - name: Setup Repository on Pod
        uses: appleboy/ssh-action@master
        with:
          host: ${{ steps.create_pod.outputs.pod_ip }}
          username: root
          key: ${{ secrets.SSH_PRIVATE_KEY }} # The key for the action to connect to the pod
          port: 22
          script: |
            # Set up the SSH environment for cloning from GitHub
            mkdir -p ~/.ssh
            echo "${{ secrets.GH_DEPLOY_KEY }}" > ~/.ssh/id_rsa
            chmod 600 ~/.ssh/id_rsa
            ssh-keyscan github.com >> ~/.ssh/known_hosts

                
            # Clone the private repository using the deploy key
            git clone "**************:${{ github.repository }}.git" /workspace
            
            # Change to the workspace directory
            cd /workspace

            # install uv
            curl -LsSf https://astral.sh/uv/install.sh | sh

            # install dependencies
            uv sync --extra cuda